import 'dart:convert';
import 'package:flutter/services.dart';
import '../../models/models.dart';
import '../../services/firestore.dart';

/// Utility class for seeding SystemPersona entities from JSON file
/// Reads data from data/system_personas.json
class SystemPersonaSeeder {
  /// Loads and parses the SystemPersona data from JSON file
  static Future<List<Map<String, dynamic>>> _loadPersonasFromJson() async {
    try {
      // Load the JSON file from assets
      final String jsonString = await rootBundle.loadString(
        'data/system_personas.json',
      );

      // Parse the JSON
      final List<dynamic> jsonData = jsonDecode(jsonString);

      // Convert to List<Map<String, dynamic>>
      return jsonData.cast<Map<String, dynamic>>();
    } catch (e) {
      throw Exception('Failed to load system personas from JSON: $e');
    }
  }

  /// Returns the list of SystemPersona entities loaded from JSON
  static Future<List<SystemPersona>> getDefaultPersonas() async {
    try {
      final jsonData = await _loadPersonasFromJson();
      final personas = <SystemPersona>[];

      for (final personaData in jsonData) {
        try {
          // Create SystemPersona object from JSON
          final persona = SystemPersona.fromJson(personaData);
          personas.add(persona);
        } catch (e) {
          throw Exception('Failed to parse persona data: $e');
        }
      }

      return personas;
    } catch (e) {
      throw Exception('Failed to get default personas: $e');
    }
  }

  /// Seeds the SystemPersona entities from JSON into Firestore
  /// Returns the list of created document IDs
  static Future<List<String>> seedDefaultPersonas() async {
    final personas = await getDefaultPersonas();
    return await FirestoreService.createSystemPersonas(personas);
  }

  /// Checks if SystemPersona entities already exist in Firestore
  /// Returns true if personas exist, false if the collection is empty
  static Future<bool> personasExist() async {
    final count = await FirestoreService.getSystemPersonaCount();
    return count > 0;
  }

  /// Seeds personas only if they don't already exist
  /// Returns the list of created document IDs, or empty list if personas already exist
  static Future<List<String>> seedIfEmpty() async {
    if (await personasExist()) {
      return [];
    }
    return await seedDefaultPersonas();
  }

  /// Gets the count of personas that would be seeded from JSON
  static Future<int> getPersonaCount() async {
    final personas = await getDefaultPersonas();
    return personas.length;
  }

  /// Validates that all personas in JSON have required fields
  static Future<bool> validatePersonaData() async {
    try {
      final personas = await getDefaultPersonas();

      for (final persona in personas) {
        // Check required fields
        if (persona.name.isEmpty) {
          throw Exception('Persona missing required field: name');
        }
        if (persona.title == null || persona.title!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: title',
          );
        }
        if (persona.description == null || persona.description!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: description',
          );
        }
        if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: avatarUrl',
          );
        }
        if (persona.isActive == null) {
          throw Exception(
            'Persona "${persona.name}" missing required field: isActive',
          );
        }
        if (persona.approach == null || persona.approach!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: approach',
          );
        }
        if (persona.coachingStyle == null || persona.coachingStyle!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: coachingStyle',
          );
        }
        if (persona.specialties == null || persona.specialties!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: specialties',
          );
        }
        if (persona.videoUrl == null || persona.videoUrl!.isEmpty) {
          throw Exception(
            'Persona "${persona.name}" missing required field: videoUrl',
          );
        }
      }

      return true;
    } catch (e) {
      throw Exception('Persona validation failed: $e');
    }
  }

  /// Gets a summary of the personas that would be seeded
  static Future<Map<String, dynamic>> getPersonaSummary() async {
    final personas = await getDefaultPersonas();

    final summary = <String, dynamic>{
      'totalCount': personas.length,
      'activeCount': personas.where((p) => p.isActive == true).length,
      'personas': personas
          .map(
            (p) => {
              'name': p.name,
              'title': p.title,
              'coachingStyle': p.coachingStyle,
              'approach': p.approach,
              'isActive': p.isActive,
              'specialtyCount': p.specialties?.length ?? 0,
            },
          )
          .toList(),
    };

    return summary;
  }
}
