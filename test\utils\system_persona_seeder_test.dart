// ignore_for_file: avoid_print

import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/utils/admin/system_persona_seeder.dart';

void main() {
  group('SystemPersonaSeeder Tests', () {
    // Initialize Flutter binding for asset loading
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should load and parse system personas from JSON', () async {
      try {
        final personas = await SystemPersonaSeeder.getDefaultPersonas();

        // Verify we have personas
        expect(personas.length, greaterThan(0));
        print('✅ Loaded ${personas.length} personas from JSON');

        // Verify each persona has required fields
        for (final persona in personas) {
          expect(persona.name.isNotEmpty, isTrue);
          expect(persona.title.isNotEmpty, isTrue);
          expect(persona.description.isNotEmpty, isTrue);
          expect(persona.avatarUrl.isNotEmpty, isTrue);
          expect(persona.approach.isNotEmpty, isTrue);
          expect(persona.coachingStyle.isNotEmpty, isTrue);
          expect(persona.specialties.isNotEmpty, isTrue);
          expect(persona.videoUrl.isNotEmpty, isTrue);
          expect(persona.catchphrase.isNotEmpty, isTrue);
        }

        print('✅ All personas have required fields');
      } catch (e) {
        fail('Failed to load personas: $e');
      }
    });

    test('should verify specific persona data from JSON', () async {
      try {
        final personas = await SystemPersonaSeeder.getDefaultPersonas();

        // Look for specific personas we know should be in the JSON
        final sagePersona = personas.firstWhere(
          (p) => p.name == 'Sage',
          orElse: () => throw Exception('Sage persona not found'),
        );

        expect(sagePersona.title, equals('Seeker of Truth'));
        expect(sagePersona.coachingStyle, equals('contemplative'));
        expect(sagePersona.approach, equals('reflective'));
        expect(sagePersona.isActive, isTrue);
        expect(sagePersona.specialties, contains('meaning-making'));
        expect(sagePersona.catchphrase, isNotEmpty);

        print('✅ Sage persona data verified');

        // Verify we have the expected personas
        final expectedPersonas = [
          'Sage',
          'Mister Iron',
          'Zen Master',
          'Brorat',
          'Professor Plan',
          'Sunny',
          'Ms. Visionary',
        ];

        for (final expectedName in expectedPersonas) {
          final found = personas.any((p) => p.name == expectedName);
          expect(
            found,
            isTrue,
            reason: 'Expected persona "$expectedName" not found',
          );
        }

        print('✅ All expected personas found in JSON data');
      } catch (e) {
        fail('Failed to verify specific persona data: $e');
      }
    });
  });
}
